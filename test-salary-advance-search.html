<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار البحث في السلف</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            direction: rtl;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 3px;
        }
        .error {
            background: #ffebee;
            color: #c62828;
        }
        .success {
            background: #e8f5e8;
            color: #2e7d32;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .test-btn {
            background: #2196f3;
            color: white;
        }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>اختبار البحث في السلف المسجلة</h1>
    
    <div class="test-section">
        <h3>1. اختبار الاتصال بـ API</h3>
        <button class="test-btn" onclick="testConnection()">اختبار الاتصال</button>
        <div id="connectionResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>2. اختبار جلب جميع السلف</h3>
        <button class="test-btn" onclick="testGetAllAdvances()">جلب جميع السلف</button>
        <div id="allAdvancesResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>3. اختبار البحث بكود الموظف</h3>
        <input type="text" id="employeeCodeSearch" placeholder="كود الموظف" value="1001">
        <button class="test-btn" onclick="testSearchByEmployeeCode()">البحث بكود الموظف</button>
        <div id="employeeCodeResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>4. اختبار البحث باسم الموظف</h3>
        <input type="text" id="employeeNameSearch" placeholder="اسم الموظف" value="أحمد">
        <button class="test-btn" onclick="testSearchByEmployeeName()">البحث باسم الموظف</button>
        <div id="employeeNameResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>5. اختبار البحث بالتاريخ</h3>
        <input type="date" id="startDate" value="2024-01-01">
        <input type="date" id="endDate" value="2024-12-31">
        <button class="test-btn" onclick="testSearchByDate()">البحث بالتاريخ</button>
        <div id="dateResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>6. اختبار الصلاحيات</h3>
        <button class="test-btn" onclick="testPermissions()">اختبار الصلاحيات</button>
        <div id="permissionsResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>7. اختبار البحث بدون معايير (جلب جميع السلف)</h3>
        <button class="test-btn" onclick="testSearchWithoutCriteria()">البحث بدون معايير</button>
        <div id="searchWithoutCriteriaResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>8. اختبار التوكن</h3>
        <input type="text" id="tokenInput" placeholder="أدخل التوكن" style="width: 300px;">
        <button class="test-btn" onclick="setToken()">تعيين التوكن</button>
        <button class="test-btn" onclick="testTokenValidity()">اختبار صحة التوكن</button>
        <div id="tokenResult" class="result"></div>
    </div>

    <script>
        const API_URL = 'http://localhost:5500';
        
        // محاولة الحصول على التوكن من localStorage
        function getToken() {
            return localStorage.getItem('token') || 'test-token';
        }

        // دالة لعرض النتائج
        function showResult(elementId, message, isError = false) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = isError ? 'result error' : 'result success';
        }

        // اختبار الاتصال
        async function testConnection() {
            try {
                const response = await fetch(`${API_URL}/api/employees`, {
                    headers: {
                        'Authorization': `Bearer ${getToken()}`
                    }
                });
                
                if (response.ok) {
                    showResult('connectionResult', 'الاتصال ناجح ✓');
                } else {
                    showResult('connectionResult', `فشل الاتصال: ${response.status} - ${response.statusText}`, true);
                }
            } catch (error) {
                showResult('connectionResult', `خطأ في الاتصال: ${error.message}`, true);
            }
        }

        // اختبار جلب جميع السلف
        async function testGetAllAdvances() {
            try {
                const response = await fetch(`${API_URL}/api/salary-advances`, {
                    headers: {
                        'Authorization': `Bearer ${getToken()}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showResult('allAdvancesResult', `تم جلب ${data.length} سلفة بنجاح ✓`);
                } else {
                    const errorText = await response.text();
                    showResult('allAdvancesResult', `فشل جلب السلف: ${response.status} - ${errorText}`, true);
                }
            } catch (error) {
                showResult('allAdvancesResult', `خطأ في جلب السلف: ${error.message}`, true);
            }
        }

        // اختبار البحث بكود الموظف
        async function testSearchByEmployeeCode() {
            const employeeCode = document.getElementById('employeeCodeSearch').value;
            if (!employeeCode) {
                showResult('employeeCodeResult', 'يرجى إدخال كود الموظف', true);
                return;
            }

            try {
                const response = await fetch(`${API_URL}/api/salary-advances/search?employee_code=${employeeCode}`, {
                    headers: {
                        'Authorization': `Bearer ${getToken()}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showResult('employeeCodeResult', `تم العثور على ${data.length} سلفة للموظف ${employeeCode} ✓`);
                } else {
                    const errorText = await response.text();
                    showResult('employeeCodeResult', `فشل البحث: ${response.status} - ${errorText}`, true);
                }
            } catch (error) {
                showResult('employeeCodeResult', `خطأ في البحث: ${error.message}`, true);
            }
        }

        // اختبار البحث باسم الموظف
        async function testSearchByEmployeeName() {
            const employeeName = document.getElementById('employeeNameSearch').value;
            if (!employeeName) {
                showResult('employeeNameResult', 'يرجى إدخال اسم الموظف', true);
                return;
            }

            try {
                const response = await fetch(`${API_URL}/api/salary-advances/search?employee_name=${encodeURIComponent(employeeName)}`, {
                    headers: {
                        'Authorization': `Bearer ${getToken()}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showResult('employeeNameResult', `تم العثور على ${data.length} سلفة للموظف "${employeeName}" ✓`);
                } else {
                    const errorText = await response.text();
                    showResult('employeeNameResult', `فشل البحث: ${response.status} - ${errorText}`, true);
                }
            } catch (error) {
                showResult('employeeNameResult', `خطأ في البحث: ${error.message}`, true);
            }
        }

        // اختبار البحث بالتاريخ
        async function testSearchByDate() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            
            if (!startDate || !endDate) {
                showResult('dateResult', 'يرجى إدخال تاريخ البداية والنهاية', true);
                return;
            }

            try {
                const response = await fetch(`${API_URL}/api/salary-advances/search?start_date=${startDate}&end_date=${endDate}`, {
                    headers: {
                        'Authorization': `Bearer ${getToken()}`
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    showResult('dateResult', `تم العثور على ${data.length} سلفة في الفترة من ${startDate} إلى ${endDate} ✓`);
                } else {
                    const errorText = await response.text();
                    showResult('dateResult', `فشل البحث: ${response.status} - ${errorText}`, true);
                }
            } catch (error) {
                showResult('dateResult', `خطأ في البحث: ${error.message}`, true);
            }
        }

        // اختبار الصلاحيات
        async function testPermissions() {
            const permissions = localStorage.getItem('permissions');
            if (!permissions) {
                showResult('permissionsResult', 'لا توجد صلاحيات محفوظة في localStorage', true);
                return;
            }

            try {
                const permissionsObj = JSON.parse(permissions);
                const salaryAdvancePermissions = [
                    'view_salary_advances',
                    'add_salary_advance',
                    'edit_salary_advance',
                    'delete_salary_advance',
                    'view_salary_advance_reports'
                ];

                const hasPermissions = salaryAdvancePermissions.filter(perm => permissionsObj[perm]);
                showResult('permissionsResult', `الصلاحيات المتاحة: ${hasPermissions.join(', ')} ✓`);
            } catch (error) {
                showResult('permissionsResult', `خطأ في قراءة الصلاحيات: ${error.message}`, true);
            }
        }

        // اختبار البحث بدون معايير
        async function testSearchWithoutCriteria() {
            try {
                const response = await fetch(`${API_URL}/api/salary-advances/search`, {
                    headers: {
                        'Authorization': `Bearer ${getToken()}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    showResult('searchWithoutCriteriaResult', `تم جلب ${data.length} سلفة بدون معايير بحث ✓`);
                } else {
                    const errorText = await response.text();
                    showResult('searchWithoutCriteriaResult', `فشل البحث: ${response.status} - ${errorText}`, true);
                }
            } catch (error) {
                showResult('searchWithoutCriteriaResult', `خطأ في البحث: ${error.message}`, true);
            }
        }

        // تعيين التوكن
        function setToken() {
            const token = document.getElementById('tokenInput').value;
            if (token) {
                localStorage.setItem('token', token);
                showResult('tokenResult', 'تم تعيين التوكن بنجاح ✓');
            } else {
                showResult('tokenResult', 'يرجى إدخال التوكن', true);
            }
        }

        // اختبار صحة التوكن
        async function testTokenValidity() {
            try {
                const response = await fetch(`${API_URL}/api/employees`, {
                    headers: {
                        'Authorization': `Bearer ${getToken()}`
                    }
                });

                if (response.ok) {
                    showResult('tokenResult', 'التوكن صحيح ✓');
                } else if (response.status === 401 || response.status === 403) {
                    showResult('tokenResult', 'التوكن غير صحيح أو منتهي الصلاحية', true);
                } else {
                    showResult('tokenResult', `خطأ في الخادم: ${response.status}`, true);
                }
            } catch (error) {
                showResult('tokenResult', `خطأ في الاتصال: ${error.message}`, true);
            }
        }

        // تشغيل اختبار الاتصال عند تحميل الصفحة
        window.onload = function() {
            testConnection();
            // عرض التوكن الحالي
            document.getElementById('tokenInput').value = getToken();
        };
    </script>
</body>
</html>
