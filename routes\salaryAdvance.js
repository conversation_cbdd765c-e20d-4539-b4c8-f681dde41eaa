const express = require('express');
const router = express.Router();
const { authenticateToken } = require('../middleware/auth');
const { logAction, createEditMessage } = require('../activityLogger');
const { cleanUpdateData, cleanInsertData, prepareUpdateQuery, prepareInsertQuery } = require('../utils/dataCleanup');

// دالة لإنشاء جدول السلف
const createSalaryAdvanceTable = async (pool) => {
  const sql = `CREATE TABLE IF NOT EXISTS salary_advances (
    id int NOT NULL AUTO_INCREMENT,
    employee_code varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'كود الموظف',
    employee_name varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'اسم الموظف',
    department varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'الإدارة',
    advance_amount decimal(10,2) NOT NULL COMMENT 'قيمة السلفة',
    advance_date date NOT NULL COMMENT 'تاريخ السلفة',
    advance_reason text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'سبب السلفة',
    payment_method varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'طريقة السداد',
    notes text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'ملاحظات إضافية',
    created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'تاريخ الإنشاء',
    updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'تاريخ التحديث',
    PRIMARY KEY (id),
    KEY idx_employee_code (employee_code),
    KEY idx_department (department),
    KEY idx_advance_date (advance_date),
    KEY idx_advance_amount (advance_amount)
  ) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='جدول السلف'`;
  
  await pool.promise().query(sql);
};

// إنشاء جدول السلف
router.get('/create-table', async (req, res) => {
  try {
    await createSalaryAdvanceTable(req.app.locals.pool);
    res.json({ message: 'تم التحقق من جدول السلف' });
  } catch (error) {
    console.error('خطأ في إنشاء جدول السلف:', error);
    res.status(500).json({ error: 'فشل في إنشاء جدول السلف' });
  }
});

// الحصول على جميع السلف
router.get('/', authenticateToken, async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    await createSalaryAdvanceTable(pool);
    
    const [rows] = await pool.promise().query(`
      SELECT 
        sa.*,
        DATE_FORMAT(sa.advance_date, '%Y-%m-%d') as advance_date_formatted,
        CASE
          WHEN e.full_name IS NOT NULL THEN e.full_name
          ELSE sa.employee_name
        END as current_employee_name,
        CASE
          WHEN e.department IS NOT NULL THEN e.department
          ELSE sa.department
        END as current_department
      FROM salary_advances sa
      LEFT JOIN employees e ON sa.employee_code = e.code
      ORDER BY sa.created_at DESC, sa.id DESC
    `);
    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب السلف:', error);
    res.status(500).json({ error: 'فشل في جلب بيانات السلف' });
  }
});

// إضافة سلفة جديدة
router.post('/', authenticateToken, async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    await createSalaryAdvanceTable(pool);
    
    const { 
      employee_code, 
      advance_amount, 
      advance_date, 
      advance_reason, 
      payment_method, 
      notes 
    } = req.body;

    // التحقق من البيانات المطلوبة
    if (!employee_code || !advance_amount || !advance_date || !advance_reason || !payment_method) {
      return res.status(400).json({ error: 'جميع الحقول المطلوبة يجب تعبئتها' });
    }

    // الحصول على بيانات الموظف
    const [employeeResult] = await pool.promise().query(
      'SELECT full_name, department, status FROM employees WHERE code = ?',
      [employee_code]
    );

    if (employeeResult.length === 0) {
      return res.status(400).json({ error: 'الموظف غير موجود' });
    }

    // التحقق من حالة الموظف
    if (employeeResult[0].status === 'مستقيل') {
      return res.status(400).json({ error: 'لا يمكن إضافة سلفة لموظف مستقيل' });
    }

    const employeeName = employeeResult[0].full_name;
    const department = employeeResult[0].department;

    const sql = `
      INSERT INTO salary_advances (
        employee_code, employee_name, department, advance_amount, 
        advance_date, advance_reason, payment_method, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `;
    
    const values = [
      employee_code,
      employeeName,
      department,
      advance_amount,
      advance_date,
      advance_reason,
      payment_method,
      notes || null
    ];
    
    const [result] = await pool.promise().query(sql, values);

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'add',
      module: 'salary_advances',
      record_id: result.insertId.toString(),
      message: `تم إضافة سلفة للموظف: ${employeeName} (كود: ${employee_code}) بقيمة: ${advance_amount} جنيه بتاريخ: ${advance_date} لسبب: ${advance_reason}`
    });

    res.status(201).json({
      message: 'تم إضافة السلفة بنجاح',
      id: result.insertId
    });
  } catch (error) {
    console.error('خطأ في إضافة السلفة:', error);
    res.status(500).json({ error: 'فشل في إضافة السلفة' });
  }
});

// تحديث سلفة
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const advanceId = req.params.id;

    const {
      employee_code,
      advance_amount,
      advance_date,
      advance_reason,
      payment_method,
      notes
    } = req.body;

    // التحقق من البيانات المطلوبة
    if (!employee_code || !advance_amount || !advance_date || !advance_reason || !payment_method) {
      return res.status(400).json({ error: 'جميع الحقول المطلوبة يجب تعبئتها' });
    }

    // الحصول على البيانات القديمة قبل التعديل
    const [oldDataResult] = await pool.promise().query(
      'SELECT * FROM salary_advances WHERE id = ?',
      [advanceId]
    );

    if (oldDataResult.length === 0) {
      return res.status(404).json({ error: 'السلفة غير موجودة' });
    }

    const oldData = oldDataResult[0];

    // الحصول على بيانات الموظف
    const [employeeResult] = await pool.promise().query(
      'SELECT full_name, department FROM employees WHERE code = ?',
      [employee_code]
    );

    if (employeeResult.length === 0) {
      return res.status(400).json({ error: 'الموظف غير موجود' });
    }

    const employeeName = employeeResult[0].full_name;
    const department = employeeResult[0].department;

    // البيانات الجديدة
    const newData = {
      employee_code,
      employee_name: employeeName,
      department,
      advance_amount: parseFloat(advance_amount),
      advance_date,
      advance_reason,
      payment_method,
      notes: notes || null
    };

    // تنظيف البيانات ومعالجة حقول التاريخ
    const cleanedData = cleanUpdateData({
      employee_code,
      employee_name: employeeName,
      department,
      advance_amount,
      advance_date,
      advance_reason,
      payment_method,
      notes: notes || null
    });

    // تحضير استعلام التحديث
    const { setClause, values } = prepareUpdateQuery(cleanedData);

    const [result] = await pool.promise().query(
      `UPDATE salary_advances SET ${setClause} WHERE id = ?`,
      [...values, advanceId]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({ error: 'السلفة غير موجودة' });
    }

    // إنشاء رسالة التعديل المفصلة
    const fieldLabels = {
      employee_code: 'كود الموظف',
      employee_name: 'اسم الموظف',
      department: 'الإدارة',
      advance_amount: 'مبلغ السلفة',
      advance_date: 'تاريخ السلفة',
      advance_reason: 'سبب السلفة',
      payment_method: 'طريقة السداد',
      notes: 'الملاحظات'
    };

    const editMessage = createEditMessage(
      `سلفة للموظف: ${employeeName} (كود: ${employee_code})`,
      oldData,
      newData,
      fieldLabels
    );

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'edit',
      module: 'salary_advances',
      record_id: advanceId.toString(),
      message: editMessage
    });

    res.json({ message: 'تم تحديث السلفة بنجاح' });
  } catch (error) {
    console.error('خطأ في تحديث السلفة:', error);
    res.status(500).json({ error: 'فشل في تحديث السلفة' });
  }
});

// حذف سلفة
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const advanceId = req.params.id;

    // الحصول على معلومات السلفة قبل الحذف
    const [advanceData] = await pool.promise().query(
      'SELECT employee_code, employee_name, advance_amount, advance_date, advance_reason FROM salary_advances WHERE id = ?',
      [advanceId]
    );

    if (advanceData.length === 0) {
      return res.status(404).json({ error: 'السلفة غير موجودة' });
    }

    const advance = advanceData[0];

    // حذف السلفة
    const [result] = await pool.promise().query(
      'DELETE FROM salary_advances WHERE id = ?',
      [advanceId]
    );

    // تسجيل النشاط
    await logAction({
      user_id: req.user?.id || null,
      username: req.user?.username || 'مجهول',
      action_type: 'delete',
      module: 'salary_advances',
      record_id: advanceId.toString(),
      message: `تم حذف سلفة للموظف: ${advance.employee_name} (كود: ${advance.employee_code}) - المبلغ: ${advance.advance_amount} جنيه - التاريخ: ${advance.advance_date} - السبب: ${advance.advance_reason}`
    });

    res.json({ message: 'تم حذف السلفة بنجاح' });
  } catch (error) {
    console.error('خطأ في حذف السلفة:', error);
    res.status(500).json({ error: 'فشل في حذف السلفة' });
  }
});

// الحصول على السلف حسب الموظف
router.get('/employee/:code', async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const { code } = req.params;
    
    const [rows] = await pool.promise().query(`
      SELECT 
        sa.*,
        DATE_FORMAT(sa.advance_date, '%Y-%m-%d') as advance_date_formatted
      FROM salary_advances sa
      WHERE sa.employee_code = ?
      ORDER BY sa.created_at DESC, sa.id DESC
    `, [code]);
    
    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب سلف الموظف:', error);
    res.status(500).json({ error: 'فشل في جلب بيانات سلف الموظف' });
  }
});

// الحصول على السلف حسب الإدارة
router.get('/department/:department', async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const { department } = req.params;
    
    const [rows] = await pool.promise().query(`
      SELECT 
        sa.*,
        DATE_FORMAT(sa.advance_date, '%Y-%m-%d') as advance_date_formatted
      FROM salary_advances sa
      WHERE sa.department = ?
      ORDER BY sa.created_at DESC, sa.id DESC
    `, [department]);
    
    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب سلف الإدارة:', error);
    res.status(500).json({ error: 'فشل في جلب بيانات سلف الإدارة' });
  }
});

// الحصول على السلف حسب نطاق التاريخ
router.get('/date-range', async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    const { start_date, end_date } = req.query;
    
    if (!start_date || !end_date) {
      return res.status(400).json({ error: 'يجب تحديد تاريخ البداية والنهاية' });
    }
    
    const [rows] = await pool.promise().query(`
      SELECT 
        sa.*,
        DATE_FORMAT(sa.advance_date, '%Y-%m-%d') as advance_date_formatted
      FROM salary_advances sa
      WHERE sa.advance_date BETWEEN ? AND ?
      ORDER BY sa.created_at DESC, sa.id DESC
    `, [start_date, end_date]);
    
    res.json(rows);
  } catch (error) {
    console.error('خطأ في جلب السلف حسب التاريخ:', error);
    res.status(500).json({ error: 'فشل في جلب بيانات السلف' });
  }
});

// البحث في السلف
router.get('/search', authenticateToken, async (req, res) => {
  try {
    const pool = req.app.locals.pool;
    await createSalaryAdvanceTable(pool);

    const {
      employee_code,
      employee_name,
      department,
      start_date,
      end_date,
      min_amount,
      max_amount
    } = req.query;

    let query = `
      SELECT
        sa.*,
        DATE_FORMAT(sa.advance_date, '%Y-%m-%d') as advance_date_formatted,
        CASE
          WHEN e.full_name IS NOT NULL THEN e.full_name
          ELSE sa.employee_name
        END as current_employee_name,
        CASE
          WHEN e.department IS NOT NULL THEN e.department
          ELSE sa.department
        END as current_department
      FROM salary_advances sa
      LEFT JOIN employees e ON sa.employee_code = e.code
      WHERE 1=1
    `;
    const params = [];

    // البحث بكود الموظف
    if (employee_code) {
      query += " AND sa.employee_code = ?";
      params.push(employee_code);
    }

    // البحث باسم الموظف
    if (employee_name) {
      query += " AND (sa.employee_name LIKE ? OR e.full_name LIKE ?)";
      params.push(`%${employee_name}%`, `%${employee_name}%`);
    }

    // البحث بالإدارة
    if (department) {
      query += " AND (sa.department = ? OR e.department = ?)";
      params.push(department, department);
    }

    // البحث بنطاق التاريخ
    if (start_date) {
      query += " AND sa.advance_date >= ?";
      params.push(start_date);
    }

    if (end_date) {
      query += " AND sa.advance_date <= ?";
      params.push(end_date);
    }

    // البحث بنطاق المبلغ
    if (min_amount) {
      query += " AND sa.advance_amount >= ?";
      params.push(min_amount);
    }

    if (max_amount) {
      query += " AND sa.advance_amount <= ?";
      params.push(max_amount);
    }

    query += " ORDER BY sa.created_at DESC, sa.id DESC";

    const [rows] = await pool.promise().query(query, params);
    res.json(rows);
  } catch (error) {
    console.error('خطأ في البحث في السلف:', error);
    res.status(500).json({ error: 'فشل في البحث في السلف' });
  }
});

module.exports = router;
