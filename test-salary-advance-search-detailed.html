<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار مفصل - البحث في السلف</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 3px; white-space: pre-wrap; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        button { padding: 10px 20px; margin: 5px; background: #2196f3; color: white; border: none; border-radius: 3px; cursor: pointer; }
        input { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 3px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
        th { background: #f5f5f5; }
    </style>
</head>
<body>
    <h1>اختبار مفصل - البحث في السلف</h1>
    
    <div class="section">
        <h3>1. تسجيل الدخول</h3>
        <input type="text" id="username" placeholder="اسم المستخدم" value="admin">
        <input type="password" id="password" placeholder="كلمة المرور" value="admin123">
        <button onclick="login()">تسجيل الدخول</button>
        <div id="loginResult" class="result"></div>
    </div>

    <div class="section">
        <h3>2. اختبار جلب جميع السلف</h3>
        <button onclick="getAllAdvances()">جلب جميع السلف</button>
        <div id="allAdvancesResult" class="result"></div>
    </div>

    <div class="section">
        <h3>3. اختبار البحث بكود الموظف</h3>
        <input type="text" id="searchEmployeeCode" placeholder="كود الموظف" value="163">
        <button onclick="searchByEmployeeCode()">البحث</button>
        <div id="employeeCodeResult" class="result"></div>
    </div>

    <div class="section">
        <h3>4. اختبار البحث باسم الموظف</h3>
        <input type="text" id="searchEmployeeName" placeholder="اسم الموظف" value="محمد">
        <button onclick="searchByEmployeeName()">البحث</button>
        <div id="employeeNameResult" class="result"></div>
    </div>

    <div class="section">
        <h3>5. اختبار البحث بالتاريخ</h3>
        <input type="date" id="searchStartDate" value="2025-01-01">
        <input type="date" id="searchEndDate" value="2025-12-31">
        <button onclick="searchByDate()">البحث</button>
        <div id="dateResult" class="result"></div>
    </div>

    <div class="section">
        <h3>6. اختبار البحث بالمبلغ</h3>
        <input type="number" id="searchMinAmount" placeholder="أقل مبلغ" value="500">
        <input type="number" id="searchMaxAmount" placeholder="أعلى مبلغ" value="2000">
        <button onclick="searchByAmount()">البحث</button>
        <div id="amountResult" class="result"></div>
    </div>

    <div class="section">
        <h3>7. اختبار البحث المركب</h3>
        <button onclick="searchCombined()">بحث مركب (كود 163 + مبلغ أكبر من 500)</button>
        <div id="combinedResult" class="result"></div>
    </div>

    <script>
        const API_URL = 'http://localhost:5500/api';
        
        function showResult(elementId, message, isError = false) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = isError ? 'result error' : 'result success';
        }

        async function login() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch(`${API_URL}/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username, password })
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    localStorage.setItem('token', result.token);
                    localStorage.setItem('permissions', JSON.stringify(result.permissions));
                    showResult('loginResult', `تم تسجيل الدخول بنجاح ✓\nالتوكن: ${result.token.substring(0, 20)}...`);
                } else {
                    showResult('loginResult', `فشل تسجيل الدخول: ${result.error}`, true);
                }
            } catch (error) {
                showResult('loginResult', `خطأ: ${error.message}`, true);
            }
        }

        async function getAllAdvances() {
            try {
                const response = await fetch(`${API_URL}/salary-advances`, {
                    headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    let message = `تم جلب ${data.length} سلفة بنجاح ✓\n\n`;
                    
                    if (data.length > 0) {
                        message += 'أول 3 سجلات:\n';
                        data.slice(0, 3).forEach((advance, index) => {
                            message += `${index + 1}. كود: ${advance.employee_code}, اسم: ${advance.employee_name}, مبلغ: ${advance.advance_amount}, تاريخ: ${advance.advance_date}\n`;
                        });
                    }
                    
                    showResult('allAdvancesResult', message);
                } else {
                    const errorText = await response.text();
                    showResult('allAdvancesResult', `فشل: ${response.status} - ${errorText}`, true);
                }
            } catch (error) {
                showResult('allAdvancesResult', `خطأ: ${error.message}`, true);
            }
        }

        async function searchByEmployeeCode() {
            const employeeCode = document.getElementById('searchEmployeeCode').value;
            await performSearch({ employee_code: employeeCode }, 'employeeCodeResult');
        }

        async function searchByEmployeeName() {
            const employeeName = document.getElementById('searchEmployeeName').value;
            await performSearch({ employee_name: employeeName }, 'employeeNameResult');
        }

        async function searchByDate() {
            const startDate = document.getElementById('searchStartDate').value;
            const endDate = document.getElementById('searchEndDate').value;
            await performSearch({ start_date: startDate, end_date: endDate }, 'dateResult');
        }

        async function searchByAmount() {
            const minAmount = document.getElementById('searchMinAmount').value;
            const maxAmount = document.getElementById('searchMaxAmount').value;
            await performSearch({ min_amount: minAmount, max_amount: maxAmount }, 'amountResult');
        }

        async function searchCombined() {
            await performSearch({ 
                employee_code: '163', 
                min_amount: '500' 
            }, 'combinedResult');
        }

        async function performSearch(params, resultElementId) {
            try {
                const queryString = new URLSearchParams(params).toString();
                const url = `${API_URL}/salary-advances/search?${queryString}`;
                
                console.log('URL البحث:', url);
                console.log('معايير البحث:', params);
                
                const response = await fetch(url, {
                    headers: { 'Authorization': `Bearer ${localStorage.getItem('token')}` }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    let message = `تم العثور على ${data.length} سلفة ✓\n\n`;
                    message += `URL: ${url}\n\n`;
                    
                    if (data.length > 0) {
                        message += 'النتائج:\n';
                        data.forEach((advance, index) => {
                            message += `${index + 1}. كود: ${advance.employee_code}, اسم: ${advance.employee_name}, مبلغ: ${advance.advance_amount}, تاريخ: ${advance.advance_date}\n`;
                        });
                    } else {
                        message += 'لا توجد نتائج مطابقة للبحث';
                    }
                    
                    showResult(resultElementId, message);
                } else {
                    const errorText = await response.text();
                    showResult(resultElementId, `فشل البحث: ${response.status} - ${errorText}\nURL: ${url}`, true);
                }
            } catch (error) {
                showResult(resultElementId, `خطأ في البحث: ${error.message}`, true);
            }
        }

        // تسجيل الدخول تلقائياً عند تحميل الصفحة
        window.onload = function() {
            const token = localStorage.getItem('token');
            if (!token) {
                login();
            } else {
                showResult('loginResult', 'تم العثور على توكن محفوظ ✓');
            }
        };
    </script>
</body>
</html>
