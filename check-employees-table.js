const mysql = require('mysql2');

// إعدادات قاعدة البيانات
const dbConfig = {
  host: "localhost",
  user: "root",
  password: "Hbkhbkhbk@123",
  database: "hassan",
  port: 3306
};

async function checkEmployeesTable() {
  const connection = mysql.createConnection(dbConfig);
  
  try {
    console.log('الاتصال بقاعدة البيانات...');
    
    // التحقق من وجود جدول employees
    const [tables] = await connection.promise().query("SHOW TABLES LIKE 'employees'");
    
    if (tables.length === 0) {
      console.log('❌ جدول employees غير موجود');
      return;
    }
    
    console.log('✅ جدول employees موجود');
    
    // التحقق من بنية الجدول
    console.log('\nبنية جدول employees:');
    const [columns] = await connection.promise().query("DESCRIBE employees");
    columns.forEach(col => {
      console.log(`- ${col.Field}: ${col.Type} ${col.Null === 'NO' ? 'NOT NULL' : 'NULL'}`);
    });
    
    // التحقق من عدد الموظفين
    const [count] = await connection.promise().query("SELECT COUNT(*) as count FROM employees");
    console.log(`\nعدد الموظفين: ${count[0].count}`);
    
    // عرض بعض الموظفين
    console.log('\nبعض الموظفين:');
    const [employees] = await connection.promise().query("SELECT code, full_name, department FROM employees LIMIT 5");
    employees.forEach(emp => {
      console.log(`- كود: ${emp.code}, اسم: ${emp.full_name}, إدارة: ${emp.department}`);
    });
    
    // التحقق من الموظفين الذين لديهم سلف
    console.log('\nالموظفين الذين لديهم سلف:');
    const [employeesWithAdvances] = await connection.promise().query(`
      SELECT DISTINCT 
        sa.employee_code, 
        sa.employee_name as advance_name,
        e.full_name as employee_name,
        e.department as employee_dept,
        sa.department as advance_dept
      FROM salary_advances sa
      LEFT JOIN employees e ON sa.employee_code = e.code
      LIMIT 5
    `);
    
    employeesWithAdvances.forEach(emp => {
      console.log(`- كود: ${emp.employee_code}`);
      console.log(`  اسم في السلف: ${emp.advance_name}`);
      console.log(`  اسم في الموظفين: ${emp.employee_name || 'غير موجود'}`);
      console.log(`  إدارة في السلف: ${emp.advance_dept}`);
      console.log(`  إدارة في الموظفين: ${emp.employee_dept || 'غير موجود'}`);
      console.log('---');
    });
    
    // اختبار استعلام البحث
    console.log('\nاختبار استعلام البحث:');
    const testQuery = `
      SELECT
        sa.*,
        DATE_FORMAT(sa.advance_date, '%Y-%m-%d') as advance_date_formatted,
        CASE
          WHEN e.full_name IS NOT NULL THEN e.full_name
          ELSE sa.employee_name
        END as current_employee_name,
        CASE
          WHEN e.department IS NOT NULL THEN e.department
          ELSE sa.department
        END as current_department
      FROM salary_advances sa
      LEFT JOIN employees e ON sa.employee_code = e.code
      WHERE sa.employee_code = '163'
    `;
    
    const [searchResults] = await connection.promise().query(testQuery);
    console.log(`نتائج البحث عن كود 163: ${searchResults.length} سجل`);
    
    searchResults.forEach(result => {
      console.log(`- كود: ${result.employee_code}, اسم: ${result.current_employee_name}, مبلغ: ${result.advance_amount}`);
    });
    
  } catch (error) {
    console.error('خطأ:', error);
  } finally {
    connection.end();
  }
}

checkEmployeesTable();
