const fetch = require('node-fetch');

const API_URL = 'http://localhost:5500/api';

async function testAPI() {
  try {
    console.log('اختبار API السلف...\n');
    
    // 1. اختبار تسجيل الدخول
    console.log('1. تسجيل الدخول...');
    const loginResponse = await fetch(`${API_URL}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username: 'admin', password: 'admin123' })
    });
    
    if (!loginResponse.ok) {
      console.log('❌ فشل تسجيل الدخول');
      return;
    }
    
    const loginResult = await loginResponse.json();
    const token = loginResult.token;
    console.log('✅ تم تسجيل الدخول بنجاح');
    
    // 2. اختبار جلب جميع السلف
    console.log('\n2. جلب جميع السلف...');
    const allAdvancesResponse = await fetch(`${API_URL}/salary-advances`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    if (allAdvancesResponse.ok) {
      const allAdvances = await allAdvancesResponse.json();
      console.log(`✅ تم جلب ${allAdvances.length} سلفة`);
      
      if (allAdvances.length > 0) {
        console.log('أول سلفة:');
        const first = allAdvances[0];
        console.log(`- كود: ${first.employee_code}`);
        console.log(`- اسم: ${first.employee_name}`);
        console.log(`- مبلغ: ${first.advance_amount}`);
        console.log(`- تاريخ: ${first.advance_date}`);
      }
    } else {
      console.log('❌ فشل جلب السلف');
      console.log('الاستجابة:', await allAdvancesResponse.text());
    }
    
    // 3. اختبار البحث بدون معايير
    console.log('\n3. اختبار البحث بدون معايير...');
    const searchEmptyResponse = await fetch(`${API_URL}/salary-advances/search`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    if (searchEmptyResponse.ok) {
      const searchEmpty = await searchEmptyResponse.json();
      console.log(`✅ البحث بدون معايير: ${searchEmpty.length} سلفة`);
    } else {
      console.log('❌ فشل البحث بدون معايير');
      console.log('الاستجابة:', await searchEmptyResponse.text());
    }
    
    // 4. اختبار البحث بكود الموظف
    console.log('\n4. اختبار البحث بكود الموظف 163...');
    const searchCodeResponse = await fetch(`${API_URL}/salary-advances/search?employee_code=163`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    if (searchCodeResponse.ok) {
      const searchCode = await searchCodeResponse.json();
      console.log(`✅ البحث بكود 163: ${searchCode.length} سلفة`);
      
      searchCode.forEach((advance, index) => {
        console.log(`${index + 1}. كود: ${advance.employee_code}, اسم: ${advance.employee_name}, مبلغ: ${advance.advance_amount}`);
      });
    } else {
      console.log('❌ فشل البحث بكود الموظف');
      console.log('الاستجابة:', await searchCodeResponse.text());
    }
    
    // 5. اختبار البحث باسم الموظف
    console.log('\n5. اختبار البحث باسم الموظف "محمد"...');
    const searchNameResponse = await fetch(`${API_URL}/salary-advances/search?employee_name=${encodeURIComponent('محمد')}`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    if (searchNameResponse.ok) {
      const searchName = await searchNameResponse.json();
      console.log(`✅ البحث باسم "محمد": ${searchName.length} سلفة`);
      
      searchName.forEach((advance, index) => {
        console.log(`${index + 1}. كود: ${advance.employee_code}, اسم: ${advance.employee_name}, مبلغ: ${advance.advance_amount}`);
      });
    } else {
      console.log('❌ فشل البحث باسم الموظف');
      console.log('الاستجابة:', await searchNameResponse.text());
    }
    
    // 6. اختبار البحث بالتاريخ
    console.log('\n6. اختبار البحث بالتاريخ...');
    const searchDateResponse = await fetch(`${API_URL}/salary-advances/search?start_date=2025-01-01&end_date=2025-12-31`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    if (searchDateResponse.ok) {
      const searchDate = await searchDateResponse.json();
      console.log(`✅ البحث بالتاريخ 2025: ${searchDate.length} سلفة`);
    } else {
      console.log('❌ فشل البحث بالتاريخ');
      console.log('الاستجابة:', await searchDateResponse.text());
    }
    
    // 7. اختبار البحث بالمبلغ
    console.log('\n7. اختبار البحث بالمبلغ (500-2000)...');
    const searchAmountResponse = await fetch(`${API_URL}/salary-advances/search?min_amount=500&max_amount=2000`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    if (searchAmountResponse.ok) {
      const searchAmount = await searchAmountResponse.json();
      console.log(`✅ البحث بالمبلغ 500-2000: ${searchAmount.length} سلفة`);
    } else {
      console.log('❌ فشل البحث بالمبلغ');
      console.log('الاستجابة:', await searchAmountResponse.text());
    }
    
  } catch (error) {
    console.error('خطأ في الاختبار:', error);
  }
}

testAPI();
