# تقرير إصلاح مشكلة البحث في جدول السلف المسجلة

## المشاكل التي تم العثور عليها وإصلاحها

### 1. مشكلة في مسارات API (URLs)

**المشكلة:**
- في ملف `salaryAdvance.js`، كانت بعض استدعاءات API تستخدم مسارات خاطئة
- مثال: `/salary-advances` بدلاً من `/api/salary-advances`

**الملفات المتأثرة:**
- `salaryAdvance.js` - السطور: 78, 117, 455, 890, 938

**الإصلاح المطبق:**
```javascript
// قبل الإصلاح
const response = await fetch(`${API_URL}/salary-advances`, {

// بعد الإصلاح  
const response = await fetch(`${API_URL}/api/salary-advances`, {
```

### 2. خطأ في أسماء الحقول في استعلام البحث

**المشكلة:**
- في ملف `routes/salaryAdvance.js`، كان يتم استخدام `sa.amount` بدلاً من `sa.advance_amount`
- هذا يسبب خطأ SQL عند البحث بنطاق المبلغ

**الملف المتأثر:**
- `routes/salaryAdvance.js` - السطور: 442, 447

**الإصلاح المطبق:**
```javascript
// قبل الإصلاح
query += " AND sa.amount >= ?";
query += " AND sa.amount <= ?";

// بعد الإصلاح
query += " AND sa.advance_amount >= ?";
query += " AND sa.advance_amount <= ?";
```

### 3. تكرار في إعداد مستمعي الأحداث

**المشكلة:**
- في دالة `setupAdvanceFilters()` كان هناك تكرار في إعداد مستمعي الأحداث
- هذا يمكن أن يسبب استدعاءات متعددة للبحث

**الملف المتأثر:**
- `salaryAdvance.js` - دالة `setupAdvanceFilters()`

**الإصلاح المطبق:**
- إزالة التكرار والاحتفاظ بإعداد واحد فقط لكل مستمع حدث

### 4. تحسين معالجة الأخطاء

**المشكلة:**
- رسائل الخطأ لم تكن واضحة بما فيه الكفاية
- لم يتم عرض تفاصيل الخطأ من الخادم

**الإصلاح المطبق:**
```javascript
// قبل الإصلاح
if (!response.ok) {
  throw new Error('فشل في جلب بيانات السلف');
}

// بعد الإصلاح
if (!response.ok) {
  const errorText = await response.text();
  console.error('خطأ في الاستجابة:', response.status, errorText);
  throw new Error(`فشل في جلب بيانات السلف: ${response.status} - ${errorText}`);
}
```

## الصلاحيات والبحث

### التحقق من الصلاحيات المطلوبة

للبحث في السلف، يحتاج المستخدم إلى الصلاحيات التالية:
- `view_salary_advances` - لعرض السلف
- `view_salary_advance_reports` - لعرض تقارير السلف

### كيفية التحقق من الصلاحيات

1. **في localStorage:**
```javascript
const permissions = JSON.parse(localStorage.getItem('permissions') || '{}');
console.log('صلاحيات السلف:', {
  view_salary_advances: permissions.view_salary_advances,
  view_salary_advance_reports: permissions.view_salary_advance_reports
});
```

2. **في قاعدة البيانات:**
```sql
SELECT permissions FROM users WHERE username = 'اسم_المستخدم';
```

## اختبار الإصلاحات

تم إنشاء ملف اختبار شامل: `test-salary-advance-search.html`

### الاختبارات المتاحة:
1. اختبار الاتصال بـ API
2. اختبار جلب جميع السلف
3. اختبار البحث بكود الموظف
4. اختبار البحث باسم الموظف
5. اختبار البحث بالتاريخ
6. اختبار الصلاحيات
7. اختبار البحث بدون معايير
8. اختبار صحة التوكن

## خطوات التشخيص المستقبلية

إذا استمرت مشكلة البحث:

### 1. فحص Console في المتصفح
```javascript
// افتح Developer Tools (F12) وتحقق من:
// - أخطاء JavaScript
// - استجابات API
// - رسائل الخطأ
```

### 2. فحص Network Tab
- تحقق من استدعاءات API
- تحقق من رموز الاستجابة (200, 401, 403, 500)
- تحقق من محتوى الاستجابة

### 3. فحص الصلاحيات
```javascript
// في Console المتصفح:
console.log('التوكن:', localStorage.getItem('token'));
console.log('الصلاحيات:', localStorage.getItem('permissions'));
```

### 4. فحص قاعدة البيانات
```sql
-- التحقق من وجود جدول السلف
SHOW TABLES LIKE 'salary_advances';

-- التحقق من بنية الجدول
DESCRIBE salary_advances;

-- التحقق من وجود بيانات
SELECT COUNT(*) FROM salary_advances;
```

## الملفات المعدلة

1. `salaryAdvance.js` - إصلاح مسارات API وتحسين معالجة الأخطاء
2. `routes/salaryAdvance.js` - إصلاح أسماء الحقول في استعلام البحث
3. `test-salary-advance-search.html` - ملف اختبار جديد للتشخيص

## التوصيات

1. **اختبار دوري:** استخدم ملف الاختبار للتحقق من عمل البحث
2. **مراقبة الأخطاء:** تحقق من console المتصفح عند حدوث مشاكل
3. **النسخ الاحتياطي:** احتفظ بنسخة احتياطية قبل أي تعديلات مستقبلية
4. **التوثيق:** وثق أي تغييرات إضافية في هذا الملف

## ملاحظات مهمة

- تأكد من أن الخادم يعمل على المنفذ 5500
- تأكد من أن قاعدة البيانات متصلة
- تأكد من وجود توكن صحيح في localStorage
- تأكد من وجود الصلاحيات المناسبة للمستخدم
